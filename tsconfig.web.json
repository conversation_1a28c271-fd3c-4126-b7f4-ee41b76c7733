{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/renderer/src/env.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.tsx",
    "src/preload/*.d.ts"
  ],
  "compilerOptions": {
    "composite": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@renderer/*": [
        "src/renderer/src/*"
      ],
      "@/*": [
        "src/renderer/src/*"
      ],
      "@types/*": ["src/types/*"]
    },
    // 允许返回any类型和不设置返回类型
    "noImplicitReturns": false,
    "noImplicitAny": false,
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false
  }
}
