{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": ["electron.vite.config.*", "src/main/**/*", "src/preload/**/*", "src/types/**/*"],
  "compilerOptions": {
    "composite": true,
    "types": ["electron-vite/node"],
    // 允许返回any类型和不设置返回类型
    "noImplicitReturns": false,
    "noImplicitAny": false,
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "baseUrl": ".",
    "paths": {
      "@types/*": ["./src/types/*"],
      "@types": ["./src/types/index"]
    },
  }
}
